'use client'

import { useState } from 'react'
import Header from '@/components/layout/Header'
import Footer from '@/components/layout/Footer'
import FileUpload from '@/components/upload/FileUpload'
import ProcessingStatus from '@/components/processing/ProcessingStatus'
import CompressionSettings from '@/components/compress/CompressionSettings'
import { PDFFile, CompressionResult } from '@/types'
import { useProcessing } from '@/hooks/useProcessing'
import { PDFProcessor } from '@/lib/pdf-processor'
import Button from '@/components/ui/Button'
import { ArrowLeft, Archive } from 'lucide-react'
import Link from 'next/link'
import { formatFileSize } from '@/lib/utils'

export default function CompressPage() {
  const [files, setFiles] = useState<PDFFile[]>([])
  const [compressionResult, setCompressionResult] = useState<CompressionResult | null>(null)
  const { state, processWithProgress, reset } = useProcessing()

  const handleFilesSelected = (selectedFiles: PDFFile[]) => {
    setFiles(selectedFiles)
    setCompressionResult(null)
  }

  const handleCompress = async () => {
    if (files.length === 0) {
      alert('Please select a PDF file to compress')
      return
    }

    try {
      const originalSize = files[0].size
      
      const result = await processWithProgress(
        async () => {
          return await PDFProcessor.compressPDF(files[0].file)
        },
        [
          { message: 'Analyzing PDF structure...', weight: 1 },
          { message: 'Optimizing images and content...', weight: 3 },
          { message: 'Finalizing compressed PDF...', weight: 1 }
        ]
      )

      if (result) {
        const compressedSize = result.size
        const compressionRatio = ((originalSize - compressedSize) / originalSize) * 100
        
        setCompressionResult({
          originalSize,
          compressedSize,
          compressionRatio
        })
      }
    } catch (error) {
      console.error('Compression failed:', error)
    }
  }

  const handleReset = () => {
    setFiles([])
    setCompressionResult(null)
    reset()
  }

  const generateFilename = () => {
    const timestamp = new Date().toISOString().slice(0, 10)
    const baseName = files[0]?.name.replace('.pdf', '') || 'document'
    return `${baseName}-compressed-${timestamp}.pdf`
  }

  return (
    <div className="min-h-screen flex flex-col">
      <Header />
      
      <main className="flex-1 bg-gray-50">
        <div className="container mx-auto px-4 py-8">
          {/* Header */}
          <div className="mb-8">
            <Link 
              href="/" 
              className="inline-flex items-center text-primary-600 hover:text-primary-700 mb-4"
            >
              <ArrowLeft className="w-4 h-4 mr-2" />
              Back to Tools
            </Link>
            
            <div className="flex items-center space-x-3 mb-4">
              <div className="w-12 h-12 bg-yellow-500 rounded-xl flex items-center justify-center">
                <Archive className="w-6 h-6 text-white" />
              </div>
              <div>
                <h1 className="text-3xl font-bold text-gray-900">Compress PDF</h1>
                <p className="text-gray-600">Reduce PDF file size while maintaining quality</p>
              </div>
            </div>
          </div>

          <div className="max-w-4xl mx-auto">
            {state.status === 'idle' && (
              <div className="space-y-8">
                {/* File Upload */}
                <div>
                  <h2 className="text-xl font-semibold text-gray-900 mb-4">
                    Step 1: Select PDF file
                  </h2>
                  <FileUpload
                    onFilesSelected={handleFilesSelected}
                    maxFiles={1}
                    multiple={false}
                  />
                </div>

                {/* File Info */}
                {files.length > 0 && (
                  <div>
                    <h2 className="text-xl font-semibold text-gray-900 mb-4">
                      Step 2: File information
                    </h2>
                    <div className="bg-white p-6 rounded-lg border border-gray-200">
                      <div className="flex items-center justify-between">
                        <div>
                          <h3 className="font-medium text-gray-900">{files[0].name}</h3>
                          <p className="text-sm text-gray-600">
                            Current size: {formatFileSize(files[0].size)}
                          </p>
                        </div>
                        <div className="text-right">
                          <p className="text-sm text-gray-600">Ready for compression</p>
                        </div>
                      </div>
                    </div>
                  </div>
                )}

                {/* Compression Settings */}
                {files.length > 0 && (
                  <div>
                    <h2 className="text-xl font-semibold text-gray-900 mb-4">
                      Step 3: Compression settings
                    </h2>
                    <CompressionSettings />
                  </div>
                )}

                {/* Compress Button */}
                {files.length > 0 && (
                  <div className="text-center">
                    <Button
                      onClick={handleCompress}
                      size="lg"
                      className="px-8"
                    >
                      <Archive className="w-5 h-5 mr-2" />
                      Compress PDF
                    </Button>
                  </div>
                )}
              </div>
            )}

            {/* Processing Status */}
            {state.status !== 'idle' && (
              <div className="space-y-6">
                <ProcessingStatus
                  state={state}
                  onReset={handleReset}
                  filename={generateFilename()}
                />

                {/* Compression Results */}
                {state.status === 'completed' && compressionResult && (
                  <div className="bg-white p-6 rounded-lg border border-gray-200">
                    <h3 className="font-medium text-gray-900 mb-4">Compression Results</h3>
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                      <div className="text-center">
                        <div className="text-2xl font-bold text-gray-900">
                          {formatFileSize(compressionResult.originalSize)}
                        </div>
                        <div className="text-sm text-gray-600">Original size</div>
                      </div>
                      <div className="text-center">
                        <div className="text-2xl font-bold text-green-600">
                          {formatFileSize(compressionResult.compressedSize)}
                        </div>
                        <div className="text-sm text-gray-600">Compressed size</div>
                      </div>
                      <div className="text-center">
                        <div className="text-2xl font-bold text-primary-600">
                          {compressionResult.compressionRatio.toFixed(1)}%
                        </div>
                        <div className="text-sm text-gray-600">Size reduction</div>
                      </div>
                    </div>
                  </div>
                )}
              </div>
            )}
          </div>
        </div>
      </main>
      
      <Footer />
    </div>
  )
}
