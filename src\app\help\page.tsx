import Header from '@/components/layout/Header'
import Footer from '@/components/layout/Footer'
import { <PERSON>, Card<PERSON>ontent, CardHeader } from '@/components/ui/Card'
import { HelpCircle, FileText, Shield, Zap } from 'lucide-react'

export default function HelpPage() {
  const faqs = [
    {
      question: "Is it safe to upload my PDF files?",
      answer: "Yes, your files are processed securely in your browser when possible. For larger files processed on our servers, all files are automatically deleted within 1 hour of upload."
    },
    {
      question: "What's the maximum file size I can upload?",
      answer: "You can upload PDF files up to 50MB in size. For larger files, consider compressing them first or splitting them into smaller parts."
    },
    {
      question: "How many files can I process at once?",
      answer: "This depends on the tool. Merge PDF allows up to 20 files, while most other tools process one file at a time for optimal performance."
    },
    {
      question: "Do I need to create an account?",
      answer: "No account is required! All tools are available for free without registration. Simply upload your files and start processing."
    },
    {
      question: "What PDF versions are supported?",
      answer: "We support all standard PDF versions from 1.0 to 2.0, including password-protected PDFs (you'll need to provide the password)."
    },
    {
      question: "Can I use these tools on mobile devices?",
      answer: "Yes! Our tools are fully responsive and work on smartphones and tablets. The interface adapts to your screen size for optimal usability."
    }
  ]

  const features = [
    {
      icon: <FileText className="w-6 h-6" />,
      title: "Multiple PDF Tools",
      description: "Merge, split, compress, convert, and edit PDFs all in one place"
    },
    {
      icon: <Shield className="w-6 h-6" />,
      title: "Secure Processing",
      description: "Your files are processed securely and deleted automatically"
    },
    {
      icon: <Zap className="w-6 h-6" />,
      title: "Fast & Efficient",
      description: "Quick processing with real-time progress indicators"
    }
  ]

  return (
    <div className="min-h-screen flex flex-col">
      <Header />
      
      <main className="flex-1 bg-gray-50">
        <div className="container mx-auto px-4 py-8">
          <div className="max-w-4xl mx-auto">
            {/* Header */}
            <div className="text-center mb-12">
              <div className="w-16 h-16 bg-primary-600 rounded-full flex items-center justify-center mx-auto mb-4">
                <HelpCircle className="w-8 h-8 text-white" />
              </div>
              <h1 className="text-4xl font-bold text-gray-900 mb-4">Help Center</h1>
              <p className="text-xl text-gray-600">
                Everything you need to know about using our PDF tools
              </p>
            </div>

            {/* Features */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-12">
              {features.map((feature, index) => (
                <Card key={index}>
                  <CardContent className="p-6 text-center">
                    <div className="w-12 h-12 bg-primary-100 text-primary-600 rounded-lg flex items-center justify-center mx-auto mb-4">
                      {feature.icon}
                    </div>
                    <h3 className="font-semibold text-gray-900 mb-2">{feature.title}</h3>
                    <p className="text-sm text-gray-600">{feature.description}</p>
                  </CardContent>
                </Card>
              ))}
            </div>

            {/* FAQ */}
            <div>
              <h2 className="text-2xl font-bold text-gray-900 mb-8 text-center">
                Frequently Asked Questions
              </h2>
              
              <div className="space-y-4">
                {faqs.map((faq, index) => (
                  <Card key={index}>
                    <CardHeader>
                      <h3 className="font-semibold text-gray-900">{faq.question}</h3>
                    </CardHeader>
                    <CardContent>
                      <p className="text-gray-600">{faq.answer}</p>
                    </CardContent>
                  </Card>
                ))}
              </div>
            </div>

            {/* Contact */}
            <div className="mt-12 text-center">
              <Card>
                <CardContent className="p-8">
                  <h3 className="text-xl font-semibold text-gray-900 mb-4">
                    Still need help?
                  </h3>
                  <p className="text-gray-600 mb-6">
                    If you can't find the answer you're looking for, feel free to contact our support team.
                  </p>
                  <div className="space-y-2">
                    <p className="text-sm text-gray-500">
                      Email: <EMAIL>
                    </p>
                    <p className="text-sm text-gray-500">
                      Response time: Usually within 24 hours
                    </p>
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>
        </div>
      </main>
      
      <Footer />
    </div>
  )
}
