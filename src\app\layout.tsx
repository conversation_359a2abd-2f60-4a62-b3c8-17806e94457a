import type { Metadata } from 'next'
import { Inter } from 'next/font/google'
import './globals.css'

const inter = Inter({ subsets: ['latin'] })

export const metadata: Metadata = {
  title: 'PDF Toolkit - Free Online PDF Tools',
  description: 'Comprehensive online PDF toolkit for merging, splitting, compressing, and converting PDF files. Free and secure PDF processing.',
  keywords: 'PDF, merge, split, compress, convert, online, free, tools',
}

export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <html lang="en">
      <body className={inter.className}>
        {children}
      </body>
    </html>
  )
}
