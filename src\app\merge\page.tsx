'use client'

import { useState } from 'react'
import Header from '@/components/layout/Header'
import Footer from '@/components/layout/Footer'
import FileUpload from '@/components/upload/FileUpload'
import ProcessingStatus from '@/components/processing/ProcessingStatus'
import FileOrderList from '@/components/merge/FileOrderList'
import { PDFFile } from '@/types'
import { useProcessing } from '@/hooks/useProcessing'
import { PDFProcessor } from '@/lib/pdf-processor'
import Button from '@/components/ui/Button'
import { ArrowLeft, Merge } from 'lucide-react'
import Link from 'next/link'

export default function MergePage() {
  const [files, setFiles] = useState<PDFFile[]>([])
  const { state, processWithProgress, reset } = useProcessing()

  const handleFilesSelected = (selectedFiles: PDFFile[]) => {
    setFiles(selectedFiles)
  }

  const handleReorderFiles = (reorderedFiles: PDFFile[]) => {
    setFiles(reorderedFiles)
  }

  const handleMerge = async () => {
    if (files.length < 2) {
      alert('Please select at least 2 PDF files to merge')
      return
    }

    try {
      await processWithProgress(
        async () => {
          const fileObjects = files.map(f => f.file)
          return await PDFProcessor.mergePDFs(fileObjects)
        },
        [
          { message: 'Preparing files for merge...', weight: 1 },
          { message: 'Merging PDF documents...', weight: 3 },
          { message: 'Finalizing merged document...', weight: 1 }
        ]
      )
    } catch (error) {
      console.error('Merge failed:', error)
    }
  }

  const handleReset = () => {
    setFiles([])
    reset()
  }

  const generateFilename = () => {
    const timestamp = new Date().toISOString().slice(0, 10)
    return `merged-pdf-${timestamp}.pdf`
  }

  return (
    <div className="min-h-screen flex flex-col">
      <Header />
      
      <main className="flex-1 bg-gray-50">
        <div className="container mx-auto px-4 py-8">
          {/* Header */}
          <div className="mb-8">
            <Link 
              href="/" 
              className="inline-flex items-center text-primary-600 hover:text-primary-700 mb-4"
            >
              <ArrowLeft className="w-4 h-4 mr-2" />
              Back to Tools
            </Link>
            
            <div className="flex items-center space-x-3 mb-4">
              <div className="w-12 h-12 bg-blue-500 rounded-xl flex items-center justify-center">
                <Merge className="w-6 h-6 text-white" />
              </div>
              <div>
                <h1 className="text-3xl font-bold text-gray-900">Merge PDF</h1>
                <p className="text-gray-600">Combine multiple PDF files into one document</p>
              </div>
            </div>
          </div>

          <div className="max-w-4xl mx-auto">
            {state.status === 'idle' && (
              <div className="space-y-8">
                {/* File Upload */}
                <div>
                  <h2 className="text-xl font-semibold text-gray-900 mb-4">
                    Step 1: Select PDF files
                  </h2>
                  <FileUpload
                    onFilesSelected={handleFilesSelected}
                    maxFiles={20}
                    multiple={true}
                  />
                </div>

                {/* File Ordering */}
                {files.length > 0 && (
                  <div>
                    <h2 className="text-xl font-semibold text-gray-900 mb-4">
                      Step 2: Arrange file order
                    </h2>
                    <FileOrderList
                      files={files}
                      onReorder={handleReorderFiles}
                    />
                  </div>
                )}

                {/* Merge Button */}
                {files.length >= 2 && (
                  <div className="text-center">
                    <Button
                      onClick={handleMerge}
                      size="lg"
                      className="px-8"
                    >
                      <Merge className="w-5 h-5 mr-2" />
                      Merge {files.length} PDFs
                    </Button>
                  </div>
                )}

                {files.length === 1 && (
                  <div className="text-center p-6 bg-yellow-50 border border-yellow-200 rounded-lg">
                    <p className="text-yellow-800">
                      Please add at least one more PDF file to merge.
                    </p>
                  </div>
                )}
              </div>
            )}

            {/* Processing Status */}
            {state.status !== 'idle' && (
              <ProcessingStatus
                state={state}
                onReset={handleReset}
                filename={generateFilename()}
              />
            )}
          </div>
        </div>
      </main>
      
      <Footer />
    </div>
  )
}
