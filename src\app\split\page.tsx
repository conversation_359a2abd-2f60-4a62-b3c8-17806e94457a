'use client'

import { useState } from 'react'
import Header from '@/components/layout/Header'
import Footer from '@/components/layout/Footer'
import FileUpload from '@/components/upload/FileUpload'
import ProcessingStatus from '@/components/processing/ProcessingStatus'
import PageSelector from '@/components/split/PageSelector'
import { PDFFile } from '@/types'
import { useProcessing } from '@/hooks/useProcessing'
import { PDFProcessor } from '@/lib/pdf-processor'
import Button from '@/components/ui/Button'
import { ArrowLeft, Split } from 'lucide-react'
import Link from 'next/link'

type SplitMode = 'all' | 'range' | 'extract'

export default function SplitPage() {
  const [files, setFiles] = useState<PDFFile[]>([])
  const [splitMode, setSplitMode] = useState<SplitMode>('all')
  const [pageRange, setPageRange] = useState<{ start: number; end: number }>({ start: 1, end: 1 })
  const [selectedPages, setSelectedPages] = useState<number[]>([])
  const [pdfInfo, setPdfInfo] = useState<{ pageCount: number } | null>(null)
  const { state, processWithProgress, reset } = useProcessing()

  const handleFilesSelected = async (selectedFiles: PDFFile[]) => {
    setFiles(selectedFiles)
    
    if (selectedFiles.length > 0) {
      try {
        const info = await PDFProcessor.getPDFInfo(selectedFiles[0].file)
        setPdfInfo({ pageCount: info.pageCount })
        setPageRange({ start: 1, end: info.pageCount })
      } catch (error) {
        console.error('Failed to get PDF info:', error)
      }
    } else {
      setPdfInfo(null)
    }
  }

  const handleSplit = async () => {
    if (files.length === 0) {
      alert('Please select a PDF file to split')
      return
    }

    try {
      await processWithProgress(
        async () => {
          const file = files[0].file
          
          switch (splitMode) {
            case 'all':
              return await PDFProcessor.splitPDF(file)
            case 'range':
              const pages = Array.from(
                { length: pageRange.end - pageRange.start + 1 }, 
                (_, i) => pageRange.start + i
              )
              return await PDFProcessor.extractPages(file, pages)
            case 'extract':
              return await PDFProcessor.extractPages(file, selectedPages)
            default:
              throw new Error('Invalid split mode')
          }
        },
        [
          { message: 'Analyzing PDF structure...', weight: 1 },
          { message: 'Splitting PDF pages...', weight: 3 },
          { message: 'Preparing download...', weight: 1 }
        ]
      )
    } catch (error) {
      console.error('Split failed:', error)
    }
  }

  const handleReset = () => {
    setFiles([])
    setPdfInfo(null)
    setSelectedPages([])
    setPageRange({ start: 1, end: 1 })
    reset()
  }

  const generateFilename = () => {
    const timestamp = new Date().toISOString().slice(0, 10)
    const baseName = files[0]?.name.replace('.pdf', '') || 'document'
    
    switch (splitMode) {
      case 'all':
        return `${baseName}-split-all-${timestamp}.zip`
      case 'range':
        return `${baseName}-pages-${pageRange.start}-${pageRange.end}-${timestamp}.pdf`
      case 'extract':
        return `${baseName}-extracted-pages-${timestamp}.pdf`
      default:
        return `${baseName}-split-${timestamp}.pdf`
    }
  }

  return (
    <div className="min-h-screen flex flex-col">
      <Header />
      
      <main className="flex-1 bg-gray-50">
        <div className="container mx-auto px-4 py-8">
          {/* Header */}
          <div className="mb-8">
            <Link 
              href="/" 
              className="inline-flex items-center text-primary-600 hover:text-primary-700 mb-4"
            >
              <ArrowLeft className="w-4 h-4 mr-2" />
              Back to Tools
            </Link>
            
            <div className="flex items-center space-x-3 mb-4">
              <div className="w-12 h-12 bg-green-500 rounded-xl flex items-center justify-center">
                <Split className="w-6 h-6 text-white" />
              </div>
              <div>
                <h1 className="text-3xl font-bold text-gray-900">Split PDF</h1>
                <p className="text-gray-600">Extract pages from your PDF or split into multiple files</p>
              </div>
            </div>
          </div>

          <div className="max-w-4xl mx-auto">
            {state.status === 'idle' && (
              <div className="space-y-8">
                {/* File Upload */}
                <div>
                  <h2 className="text-xl font-semibold text-gray-900 mb-4">
                    Step 1: Select PDF file
                  </h2>
                  <FileUpload
                    onFilesSelected={handleFilesSelected}
                    maxFiles={1}
                    multiple={false}
                  />
                </div>

                {/* Split Options */}
                {files.length > 0 && pdfInfo && (
                  <div>
                    <h2 className="text-xl font-semibold text-gray-900 mb-4">
                      Step 2: Choose split method
                    </h2>
                    <PageSelector
                      pageCount={pdfInfo.pageCount}
                      splitMode={splitMode}
                      onSplitModeChange={setSplitMode}
                      pageRange={pageRange}
                      onPageRangeChange={setPageRange}
                      selectedPages={selectedPages}
                      onSelectedPagesChange={setSelectedPages}
                    />
                  </div>
                )}

                {/* Split Button */}
                {files.length > 0 && pdfInfo && (
                  <div className="text-center">
                    <Button
                      onClick={handleSplit}
                      size="lg"
                      className="px-8"
                    >
                      <Split className="w-5 h-5 mr-2" />
                      Split PDF
                    </Button>
                  </div>
                )}
              </div>
            )}

            {/* Processing Status */}
            {state.status !== 'idle' && (
              <ProcessingStatus
                state={state}
                onReset={handleReset}
                filename={generateFilename()}
              />
            )}
          </div>
        </div>
      </main>
      
      <Footer />
    </div>
  )
}
