'use client'

import { useState } from 'react'
import { Card } from '@/components/ui/Card'

type CompressionLevel = 'low' | 'medium' | 'high'

export default function CompressionSettings() {
  const [compressionLevel, setCompressionLevel] = useState<CompressionLevel>('medium')

  const compressionOptions = [
    {
      level: 'low' as CompressionLevel,
      name: 'Low Compression',
      description: 'Minimal compression, best quality',
      expectedReduction: '10-20%',
      recommended: 'For documents with high-quality images'
    },
    {
      level: 'medium' as CompressionLevel,
      name: 'Medium Compression',
      description: 'Balanced compression and quality',
      expectedReduction: '30-50%',
      recommended: 'Recommended for most documents'
    },
    {
      level: 'high' as CompressionLevel,
      name: 'High Compression',
      description: 'Maximum compression, reduced quality',
      expectedReduction: '50-70%',
      recommended: 'For documents where file size is critical'
    }
  ]

  return (
    <Card>
      <div className="p-6">
        <h3 className="font-medium text-gray-900 mb-4">Compression Level</h3>
        
        <div className="space-y-4">
          {compressionOptions.map((option) => (
            <label
              key={option.level}
              className={`block p-4 border rounded-lg cursor-pointer transition-colors ${
                compressionLevel === option.level
                  ? 'border-primary-500 bg-primary-50'
                  : 'border-gray-200 hover:border-gray-300'
              }`}
            >
              <div className="flex items-start space-x-3">
                <input
                  type="radio"
                  name="compressionLevel"
                  value={option.level}
                  checked={compressionLevel === option.level}
                  onChange={(e) => setCompressionLevel(e.target.value as CompressionLevel)}
                  className="mt-1"
                />
                <div className="flex-1">
                  <div className="flex items-center justify-between mb-1">
                    <h4 className="font-medium text-gray-900">{option.name}</h4>
                    <span className="text-sm font-medium text-primary-600">
                      {option.expectedReduction}
                    </span>
                  </div>
                  <p className="text-sm text-gray-600 mb-2">{option.description}</p>
                  <p className="text-xs text-gray-500">{option.recommended}</p>
                </div>
              </div>
            </label>
          ))}
        </div>

        <div className="mt-6 p-4 bg-blue-50 border border-blue-200 rounded-lg">
          <div className="flex items-start space-x-2">
            <div className="w-5 h-5 bg-blue-500 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
              <span className="text-white text-xs font-bold">i</span>
            </div>
            <div className="text-sm text-blue-800">
              <p className="font-medium mb-1">Compression Tips:</p>
              <ul className="space-y-1 text-xs">
                <li>• Compression results vary depending on your PDF content</li>
                <li>• Documents with many images typically compress more</li>
                <li>• Text-only documents may see minimal size reduction</li>
                <li>• Your original file is never modified</li>
              </ul>
            </div>
          </div>
        </div>
      </div>
    </Card>
  )
}
