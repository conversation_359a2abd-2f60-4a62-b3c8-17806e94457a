import Link from 'next/link'
import { Tool } from '@/types'
import { Card } from '@/components/ui/Card'
import ToolIcon from '@/components/ui/ToolIcon'
import { cn } from '@/lib/utils'

interface ToolCardProps {
  tool: Tool
}

export default function ToolCard({ tool }: ToolCardProps) {
  return (
    <Link href={tool.path} className="block group">
      <Card hover className="h-full transition-all duration-200 group-hover:scale-105">
        <div className="p-6 text-center">
          {/* Icon */}
          <div className={cn(
            'w-16 h-16 mx-auto mb-4 rounded-xl flex items-center justify-center text-white transition-transform duration-200 group-hover:scale-110',
            tool.color
          )}>
            <ToolIcon icon={tool.icon} className="w-8 h-8" />
          </div>
          
          {/* Title */}
          <h3 className="text-lg font-semibold text-gray-900 mb-2 group-hover:text-primary-600 transition-colors">
            {tool.name}
          </h3>
          
          {/* Description */}
          <p className="text-sm text-gray-600 leading-relaxed">
            {tool.description}
          </p>
        </div>
      </Card>
    </Link>
  )
}
