'use client'

import { useState } from 'react'
import { tools, categories } from '@/data/tools'
import ToolCard from './ToolCard'
import Button from '@/components/ui/Button'

export default function ToolGrid() {
  const [activeCategory, setActiveCategory] = useState('all')

  const filteredTools = activeCategory === 'all' 
    ? tools 
    : tools.filter(tool => tool.category === activeCategory)

  return (
    <div>
      {/* Category Filter */}
      <div className="flex flex-wrap justify-center gap-2 mb-8">
        {categories.map((category) => (
          <Button
            key={category.id}
            variant={activeCategory === category.id ? 'primary' : 'outline'}
            size="sm"
            onClick={() => setActiveCategory(category.id)}
            className="flex items-center space-x-2"
          >
            <span>{category.name}</span>
            <span className={`text-xs px-1.5 py-0.5 rounded-full ${
              activeCategory === category.id 
                ? 'bg-white/20 text-white' 
                : 'bg-gray-100 text-gray-600'
            }`}>
              {category.count}
            </span>
          </Button>
        ))}
      </div>

      {/* Tools Grid */}
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
        {filteredTools.map((tool) => (
          <ToolCard key={tool.id} tool={tool} />
        ))}
      </div>

      {/* Empty State */}
      {filteredTools.length === 0 && (
        <div className="text-center py-12">
          <p className="text-gray-500">No tools found in this category.</p>
        </div>
      )}
    </div>
  )
}
