import Link from 'next/link'
import { Heart } from 'lucide-react'

export default function Footer() {
  return (
    <footer className="bg-gray-50 border-t border-gray-200">
      <div className="container mx-auto px-4 py-12">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
          {/* Company Info */}
          <div className="col-span-1 md:col-span-2">
            <div className="flex items-center space-x-2 mb-4">
              <div className="w-8 h-8 bg-primary-600 rounded-lg flex items-center justify-center">
                <span className="text-white font-bold text-lg">P</span>
              </div>
              <span className="text-xl font-bold text-gray-900">PDF Toolkit</span>
            </div>
            <p className="text-gray-600 mb-4 max-w-md">
              Free online PDF tools to help you work with PDF files. 
              Merge, split, compress, convert, and edit your PDFs with ease.
            </p>
            <div className="flex items-center text-sm text-gray-500">
              <span>Made with</span>
              <Heart className="w-4 h-4 mx-1 text-red-500 fill-current" />
              <span>for productivity</span>
            </div>
          </div>

          {/* Tools */}
          <div>
            <h3 className="font-semibold text-gray-900 mb-4">Popular Tools</h3>
            <ul className="space-y-2">
              <li>
                <Link href="/merge" className="text-gray-600 hover:text-primary-600 transition-colors">
                  Merge PDF
                </Link>
              </li>
              <li>
                <Link href="/split" className="text-gray-600 hover:text-primary-600 transition-colors">
                  Split PDF
                </Link>
              </li>
              <li>
                <Link href="/compress" className="text-gray-600 hover:text-primary-600 transition-colors">
                  Compress PDF
                </Link>
              </li>
              <li>
                <Link href="/pdf-to-word" className="text-gray-600 hover:text-primary-600 transition-colors">
                  PDF to Word
                </Link>
              </li>
            </ul>
          </div>

          {/* Support */}
          <div>
            <h3 className="font-semibold text-gray-900 mb-4">Support</h3>
            <ul className="space-y-2">
              <li>
                <Link href="/help" className="text-gray-600 hover:text-primary-600 transition-colors">
                  Help Center
                </Link>
              </li>
              <li>
                <Link href="/faq" className="text-gray-600 hover:text-primary-600 transition-colors">
                  FAQ
                </Link>
              </li>
              <li>
                <Link href="/privacy" className="text-gray-600 hover:text-primary-600 transition-colors">
                  Privacy Policy
                </Link>
              </li>
              <li>
                <Link href="/terms" className="text-gray-600 hover:text-primary-600 transition-colors">
                  Terms of Service
                </Link>
              </li>
            </ul>
          </div>
        </div>

        <div className="border-t border-gray-200 mt-8 pt-8 text-center">
          <p className="text-gray-500 text-sm">
            © 2024 PDF Toolkit. All rights reserved. Your files are processed securely and deleted automatically.
          </p>
        </div>
      </div>
    </footer>
  )
}
