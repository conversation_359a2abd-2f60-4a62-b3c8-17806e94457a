'use client'

import { useState } from 'react'
import { PDFFile } from '@/types'
import { Card } from '@/components/ui/Card'
import Button from '@/components/ui/Button'
import { formatFileSize } from '@/lib/utils'
import { 
  File, 
  GripVertical, 
  ChevronUp, 
  ChevronDown, 
  X,
  Eye 
} from 'lucide-react'

interface FileOrderListProps {
  files: PDFFile[]
  onReorder: (files: PDFFile[]) => void
}

export default function FileOrderList({ files, onReorder }: FileOrderListProps) {
  const [draggedIndex, setDraggedIndex] = useState<number | null>(null)

  const moveFile = (fromIndex: number, toIndex: number) => {
    const newFiles = [...files]
    const [movedFile] = newFiles.splice(fromIndex, 1)
    newFiles.splice(toIndex, 0, movedFile)
    onReorder(newFiles)
  }

  const moveUp = (index: number) => {
    if (index > 0) {
      moveFile(index, index - 1)
    }
  }

  const moveDown = (index: number) => {
    if (index < files.length - 1) {
      moveFile(index, index + 1)
    }
  }

  const removeFile = (index: number) => {
    const newFiles = files.filter((_, i) => i !== index)
    onReorder(newFiles)
  }

  const handleDragStart = (e: React.DragEvent, index: number) => {
    setDraggedIndex(index)
    e.dataTransfer.effectAllowed = 'move'
  }

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault()
    e.dataTransfer.dropEffect = 'move'
  }

  const handleDrop = (e: React.DragEvent, dropIndex: number) => {
    e.preventDefault()
    if (draggedIndex !== null && draggedIndex !== dropIndex) {
      moveFile(draggedIndex, dropIndex)
    }
    setDraggedIndex(null)
  }

  const handleDragEnd = () => {
    setDraggedIndex(null)
  }

  return (
    <Card>
      <div className="p-6">
        <div className="flex items-center justify-between mb-4">
          <h3 className="font-medium text-gray-900">
            Files to merge ({files.length})
          </h3>
          <p className="text-sm text-gray-500">
            Drag to reorder or use arrow buttons
          </p>
        </div>

        <div className="space-y-2">
          {files.map((file, index) => (
            <div
              key={file.id}
              draggable
              onDragStart={(e) => handleDragStart(e, index)}
              onDragOver={handleDragOver}
              onDrop={(e) => handleDrop(e, index)}
              onDragEnd={handleDragEnd}
              className={`flex items-center p-4 bg-white border rounded-lg transition-all ${
                draggedIndex === index 
                  ? 'opacity-50 scale-95' 
                  : 'hover:border-primary-200 hover:shadow-sm'
              }`}
            >
              {/* Drag Handle */}
              <div className="flex items-center mr-3 cursor-move text-gray-400 hover:text-gray-600">
                <GripVertical className="w-5 h-5" />
              </div>

              {/* Order Number */}
              <div className="flex items-center justify-center w-8 h-8 bg-primary-100 text-primary-700 rounded-full text-sm font-medium mr-3">
                {index + 1}
              </div>

              {/* File Info */}
              <div className="flex items-center flex-1 min-w-0">
                <File className="w-5 h-5 text-red-600 mr-3 flex-shrink-0" />
                <div className="min-w-0 flex-1">
                  <p className="text-sm font-medium text-gray-900 truncate">
                    {file.name}
                  </p>
                  <p className="text-xs text-gray-500">
                    {formatFileSize(file.size)}
                    {file.pages && ` • ${file.pages} pages`}
                  </p>
                </div>
              </div>

              {/* Actions */}
              <div className="flex items-center space-x-1 ml-4">
                {/* Preview Button */}
                <Button
                  variant="ghost"
                  size="sm"
                  className="text-gray-400 hover:text-primary-600"
                  title="Preview"
                >
                  <Eye className="w-4 h-4" />
                </Button>

                {/* Move Up */}
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => moveUp(index)}
                  disabled={index === 0}
                  className="text-gray-400 hover:text-gray-600 disabled:opacity-30"
                  title="Move up"
                >
                  <ChevronUp className="w-4 h-4" />
                </Button>

                {/* Move Down */}
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => moveDown(index)}
                  disabled={index === files.length - 1}
                  className="text-gray-400 hover:text-gray-600 disabled:opacity-30"
                  title="Move down"
                >
                  <ChevronDown className="w-4 h-4" />
                </Button>

                {/* Remove */}
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => removeFile(index)}
                  className="text-gray-400 hover:text-red-600"
                  title="Remove file"
                >
                  <X className="w-4 h-4" />
                </Button>
              </div>
            </div>
          ))}
        </div>

        {files.length === 0 && (
          <div className="text-center py-8 text-gray-500">
            No files selected for merging
          </div>
        )}
      </div>
    </Card>
  )
}
