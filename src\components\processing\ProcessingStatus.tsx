'use client'

import { CheckCircle, AlertCircle, Loader2, Download } from 'lucide-react'
import { ProcessingState } from '@/types'
import { Card } from '@/components/ui/Card'
import Progress from '@/components/ui/Progress'
import Button from '@/components/ui/Button'
import { downloadFile } from '@/lib/utils'

interface ProcessingStatusProps {
  state: ProcessingState
  onDownload?: () => void
  onReset?: () => void
  filename?: string
}

export default function ProcessingStatus({ 
  state, 
  onDownload, 
  onReset,
  filename = 'processed-file.pdf'
}: ProcessingStatusProps) {
  const handleDownload = () => {
    if (state.result) {
      downloadFile(state.result, filename)
      onDownload?.()
    }
  }

  const getStatusIcon = () => {
    switch (state.status) {
      case 'uploading':
      case 'processing':
        return <Loader2 className="w-6 h-6 animate-spin text-primary-600" />
      case 'completed':
        return <CheckCircle className="w-6 h-6 text-green-600" />
      case 'error':
        return <AlertCircle className="w-6 h-6 text-red-600" />
      default:
        return null
    }
  }

  const getStatusColor = () => {
    switch (state.status) {
      case 'uploading':
      case 'processing':
        return 'primary'
      case 'completed':
        return 'success'
      case 'error':
        return 'error'
      default:
        return 'primary'
    }
  }

  if (state.status === 'idle') {
    return null
  }

  return (
    <Card className="w-full max-w-md mx-auto">
      <div className="p-6 text-center space-y-4">
        {/* Status Icon */}
        <div className="flex justify-center">
          {getStatusIcon()}
        </div>

        {/* Status Message */}
        <div>
          <h3 className="text-lg font-semibold text-gray-900 mb-2">
            {state.status === 'uploading' && 'Uploading files...'}
            {state.status === 'processing' && 'Processing your PDF...'}
            {state.status === 'completed' && 'Processing complete!'}
            {state.status === 'error' && 'Something went wrong'}
          </h3>
          
          <p className="text-sm text-gray-600">
            {state.message}
          </p>
        </div>

        {/* Progress Bar */}
        {(state.status === 'uploading' || state.status === 'processing') && (
          <div className="space-y-2">
            <Progress 
              value={state.progress} 
              color={getStatusColor() as any}
              className="w-full"
            />
            <p className="text-xs text-gray-500">
              {Math.round(state.progress)}% complete
            </p>
          </div>
        )}

        {/* Error Message */}
        {state.status === 'error' && state.error && (
          <div className="p-3 bg-red-50 border border-red-200 rounded-lg">
            <p className="text-sm text-red-700">{state.error}</p>
          </div>
        )}

        {/* Action Buttons */}
        <div className="flex flex-col sm:flex-row gap-3 justify-center">
          {state.status === 'completed' && state.result && (
            <Button onClick={handleDownload} className="flex items-center space-x-2">
              <Download className="w-4 h-4" />
              <span>Download Result</span>
            </Button>
          )}
          
          {(state.status === 'completed' || state.status === 'error') && onReset && (
            <Button variant="outline" onClick={onReset}>
              Process Another File
            </Button>
          )}
        </div>
      </div>
    </Card>
  )
}
