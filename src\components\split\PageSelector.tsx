'use client'

import { Card } from '@/components/ui/Card'
import <PERSON><PERSON> from '@/components/ui/Button'

interface PageSelectorProps {
  pageCount: number
  splitMode: 'all' | 'range' | 'extract'
  onSplitModeChange: (mode: 'all' | 'range' | 'extract') => void
  pageRange: { start: number; end: number }
  onPageRangeChange: (range: { start: number; end: number }) => void
  selectedPages: number[]
  onSelectedPagesChange: (pages: number[]) => void
}

export default function PageSelector({
  pageCount,
  splitMode,
  onSplitModeChange,
  pageRange,
  onPageRangeChange,
  selectedPages,
  onSelectedPagesChange
}: PageSelectorProps) {
  const handlePageToggle = (pageNumber: number) => {
    if (selectedPages.includes(pageNumber)) {
      onSelectedPagesChange(selectedPages.filter(p => p !== pageNumber))
    } else {
      onSelectedPagesChange([...selectedPages, pageNumber].sort((a, b) => a - b))
    }
  }

  const selectAllPages = () => {
    onSelectedPagesChange(Array.from({ length: pageCount }, (_, i) => i + 1))
  }

  const clearSelection = () => {
    onSelectedPagesChange([])
  }

  return (
    <Card>
      <div className="p-6 space-y-6">
        {/* Split Mode Options */}
        <div>
          <h3 className="font-medium text-gray-900 mb-3">Split Method</h3>
          <div className="space-y-3">
            <label className="flex items-start space-x-3 cursor-pointer">
              <input
                type="radio"
                name="splitMode"
                value="all"
                checked={splitMode === 'all'}
                onChange={(e) => onSplitModeChange(e.target.value as any)}
                className="mt-1"
              />
              <div>
                <div className="font-medium text-gray-900">Split into individual pages</div>
                <div className="text-sm text-gray-600">
                  Create {pageCount} separate PDF files, one for each page
                </div>
              </div>
            </label>

            <label className="flex items-start space-x-3 cursor-pointer">
              <input
                type="radio"
                name="splitMode"
                value="range"
                checked={splitMode === 'range'}
                onChange={(e) => onSplitModeChange(e.target.value as any)}
                className="mt-1"
              />
              <div>
                <div className="font-medium text-gray-900">Extract page range</div>
                <div className="text-sm text-gray-600">
                  Extract a continuous range of pages into a new PDF
                </div>
              </div>
            </label>

            <label className="flex items-start space-x-3 cursor-pointer">
              <input
                type="radio"
                name="splitMode"
                value="extract"
                checked={splitMode === 'extract'}
                onChange={(e) => onSplitModeChange(e.target.value as any)}
                className="mt-1"
              />
              <div>
                <div className="font-medium text-gray-900">Extract specific pages</div>
                <div className="text-sm text-gray-600">
                  Select individual pages to extract into a new PDF
                </div>
              </div>
            </label>
          </div>
        </div>

        {/* Page Range Input */}
        {splitMode === 'range' && (
          <div>
            <h4 className="font-medium text-gray-900 mb-3">Page Range</h4>
            <div className="flex items-center space-x-4">
              <div>
                <label className="block text-sm text-gray-600 mb-1">From page</label>
                <input
                  type="number"
                  min={1}
                  max={pageCount}
                  value={pageRange.start}
                  onChange={(e) => onPageRangeChange({
                    ...pageRange,
                    start: Math.max(1, Math.min(parseInt(e.target.value) || 1, pageCount))
                  })}
                  className="w-20 px-3 py-2 border border-gray-300 rounded-md focus:ring-primary-500 focus:border-primary-500"
                />
              </div>
              <div>
                <label className="block text-sm text-gray-600 mb-1">To page</label>
                <input
                  type="number"
                  min={pageRange.start}
                  max={pageCount}
                  value={pageRange.end}
                  onChange={(e) => onPageRangeChange({
                    ...pageRange,
                    end: Math.max(pageRange.start, Math.min(parseInt(e.target.value) || pageCount, pageCount))
                  })}
                  className="w-20 px-3 py-2 border border-gray-300 rounded-md focus:ring-primary-500 focus:border-primary-500"
                />
              </div>
              <div className="text-sm text-gray-600 mt-6">
                of {pageCount} pages
              </div>
            </div>
          </div>
        )}

        {/* Page Selection */}
        {splitMode === 'extract' && (
          <div>
            <div className="flex items-center justify-between mb-3">
              <h4 className="font-medium text-gray-900">Select Pages</h4>
              <div className="space-x-2">
                <Button variant="ghost" size="sm" onClick={selectAllPages}>
                  Select All
                </Button>
                <Button variant="ghost" size="sm" onClick={clearSelection}>
                  Clear
                </Button>
              </div>
            </div>
            
            <div className="grid grid-cols-8 sm:grid-cols-12 gap-2 max-h-64 overflow-y-auto">
              {Array.from({ length: pageCount }, (_, i) => i + 1).map((pageNumber) => (
                <button
                  key={pageNumber}
                  onClick={() => handlePageToggle(pageNumber)}
                  className={`w-10 h-10 text-sm rounded-md border transition-colors ${
                    selectedPages.includes(pageNumber)
                      ? 'bg-primary-600 text-white border-primary-600'
                      : 'bg-white text-gray-700 border-gray-300 hover:border-primary-300 hover:bg-primary-50'
                  }`}
                >
                  {pageNumber}
                </button>
              ))}
            </div>
            
            {selectedPages.length > 0 && (
              <div className="mt-3 text-sm text-gray-600">
                Selected pages: {selectedPages.join(', ')}
              </div>
            )}
          </div>
        )}

        {/* Summary */}
        <div className="bg-gray-50 p-4 rounded-lg">
          <h4 className="font-medium text-gray-900 mb-2">Summary</h4>
          <div className="text-sm text-gray-600">
            {splitMode === 'all' && `Will create ${pageCount} individual PDF files`}
            {splitMode === 'range' && `Will extract pages ${pageRange.start}-${pageRange.end} (${pageRange.end - pageRange.start + 1} pages)`}
            {splitMode === 'extract' && `Will extract ${selectedPages.length} selected pages`}
          </div>
        </div>
      </div>
    </Card>
  )
}
