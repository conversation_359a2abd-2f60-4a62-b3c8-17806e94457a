import { 
  Merge, 
  Split, 
  RotateCw, 
  Trash2, 
  FileText, 
  File, 
  FileSpreadsheet, 
  Presentation, 
  Image, 
  Archive, 
  Droplets 
} from 'lucide-react'

interface ToolIconProps {
  icon: string
  className?: string
}

export default function ToolIcon({ icon, className = "w-6 h-6" }: ToolIconProps) {
  const iconMap = {
    merge: Merge,
    split: Split,
    rotate: RotateCw,
    delete: Trash2,
    word: FileText,
    pdf: File,
    excel: FileSpreadsheet,
    powerpoint: Presentation,
    image: Image,
    compress: Archive,
    watermark: Droplets,
  }

  const IconComponent = iconMap[icon as keyof typeof iconMap] || File

  return <IconComponent className={className} />
}
