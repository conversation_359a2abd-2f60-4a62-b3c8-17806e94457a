'use client'

import { useCallback, useState } from 'react'
import { useDropzone } from 'react-dropzone'
import { Upload, File, X, AlertCircle } from 'lucide-react'
import { PDFFile } from '@/types'
import { formatFileSize, generateId } from '@/lib/utils'
import { Card } from '@/components/ui/Card'
import Button from '@/components/ui/Button'

interface FileUploadProps {
  onFilesSelected: (files: PDFFile[]) => void
  maxFiles?: number
  acceptedFileTypes?: string[]
  maxFileSize?: number // in bytes
  multiple?: boolean
}

export default function FileUpload({
  onFilesSelected,
  maxFiles = 10,
  acceptedFileTypes = ['.pdf'],
  maxFileSize = 50 * 1024 * 1024, // 50MB
  multiple = true
}: FileUploadProps) {
  const [uploadedFiles, setUploadedFiles] = useState<PDFFile[]>([])
  const [errors, setErrors] = useState<string[]>([])

  const onDrop = useCallback((acceptedFiles: File[], rejectedFiles: any[]) => {
    setErrors([])
    const newErrors: string[] = []

    // Handle rejected files
    rejectedFiles.forEach((rejection) => {
      rejection.errors.forEach((error: any) => {
        if (error.code === 'file-too-large') {
          newErrors.push(`${rejection.file.name}: File is too large (max ${formatFileSize(maxFileSize)})`)
        } else if (error.code === 'file-invalid-type') {
          newErrors.push(`${rejection.file.name}: Invalid file type (only PDF files allowed)`)
        } else {
          newErrors.push(`${rejection.file.name}: ${error.message}`)
        }
      })
    })

    // Process accepted files
    const newFiles: PDFFile[] = acceptedFiles.map(file => ({
      id: generateId(),
      file,
      name: file.name,
      size: file.size
    }))

    // Check total file count
    if (uploadedFiles.length + newFiles.length > maxFiles) {
      newErrors.push(`Maximum ${maxFiles} files allowed`)
      setErrors(newErrors)
      return
    }

    const updatedFiles = [...uploadedFiles, ...newFiles]
    setUploadedFiles(updatedFiles)
    onFilesSelected(updatedFiles)
    
    if (newErrors.length > 0) {
      setErrors(newErrors)
    }
  }, [uploadedFiles, maxFiles, maxFileSize, onFilesSelected])

  const { getRootProps, getInputProps, isDragActive, isDragReject } = useDropzone({
    onDrop,
    accept: {
      'application/pdf': acceptedFileTypes
    },
    maxSize: maxFileSize,
    multiple,
    disabled: uploadedFiles.length >= maxFiles
  })

  const removeFile = (fileId: string) => {
    const updatedFiles = uploadedFiles.filter(f => f.id !== fileId)
    setUploadedFiles(updatedFiles)
    onFilesSelected(updatedFiles)
  }

  const clearAll = () => {
    setUploadedFiles([])
    setErrors([])
    onFilesSelected([])
  }

  return (
    <div className="space-y-4">
      {/* Upload Zone */}
      <Card className={`border-2 border-dashed transition-colors ${
        isDragActive 
          ? isDragReject 
            ? 'border-red-300 bg-red-50' 
            : 'border-primary-300 bg-primary-50'
          : 'border-gray-300 hover:border-primary-300'
      }`}>
        <div
          {...getRootProps()}
          className="p-8 text-center cursor-pointer"
        >
          <input {...getInputProps()} />
          
          <div className="space-y-4">
            <div className={`w-16 h-16 mx-auto rounded-full flex items-center justify-center ${
              isDragActive 
                ? isDragReject 
                  ? 'bg-red-100 text-red-600' 
                  : 'bg-primary-100 text-primary-600'
                : 'bg-gray-100 text-gray-600'
            }`}>
              <Upload className="w-8 h-8" />
            </div>
            
            <div>
              <h3 className="text-lg font-semibold text-gray-900 mb-2">
                {isDragActive 
                  ? isDragReject 
                    ? 'Invalid file type' 
                    : 'Drop your files here'
                  : 'Choose PDF files or drag & drop'
                }
              </h3>
              <p className="text-sm text-gray-600">
                {multiple ? `Up to ${maxFiles} files` : 'Single file only'} • 
                Max {formatFileSize(maxFileSize)} per file
              </p>
            </div>
            
            <Button variant="outline" size="sm" type="button">
              Select Files
            </Button>
          </div>
        </div>
      </Card>

      {/* Error Messages */}
      {errors.length > 0 && (
        <Card className="border-red-200 bg-red-50">
          <div className="p-4">
            <div className="flex items-start space-x-2">
              <AlertCircle className="w-5 h-5 text-red-600 mt-0.5 flex-shrink-0" />
              <div className="space-y-1">
                {errors.map((error, index) => (
                  <p key={index} className="text-sm text-red-700">{error}</p>
                ))}
              </div>
            </div>
          </div>
        </Card>
      )}

      {/* Uploaded Files */}
      {uploadedFiles.length > 0 && (
        <Card>
          <div className="p-4">
            <div className="flex items-center justify-between mb-4">
              <h4 className="font-medium text-gray-900">
                Uploaded Files ({uploadedFiles.length})
              </h4>
              <Button variant="ghost" size="sm" onClick={clearAll}>
                Clear All
              </Button>
            </div>
            
            <div className="space-y-2">
              {uploadedFiles.map((file) => (
                <div key={file.id} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                  <div className="flex items-center space-x-3">
                    <File className="w-5 h-5 text-red-600" />
                    <div>
                      <p className="text-sm font-medium text-gray-900">{file.name}</p>
                      <p className="text-xs text-gray-500">{formatFileSize(file.size)}</p>
                    </div>
                  </div>
                  
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => removeFile(file.id)}
                    className="text-gray-400 hover:text-red-600"
                  >
                    <X className="w-4 h-4" />
                  </Button>
                </div>
              ))}
            </div>
          </div>
        </Card>
      )}
    </div>
  )
}
