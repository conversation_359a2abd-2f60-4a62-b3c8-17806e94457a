import { Tool } from '@/types'

export const tools: Tool[] = [
  // Organize Tools
  {
    id: 'merge',
    name: 'Merge PDF',
    description: 'Combine multiple PDF files into one document',
    icon: 'merge',
    path: '/merge',
    category: 'organize',
    color: 'bg-blue-500'
  },
  {
    id: 'split',
    name: 'Split PDF',
    description: 'Extract pages from your PDF or split into multiple files',
    icon: 'split',
    path: '/split',
    category: 'organize',
    color: 'bg-green-500'
  },
  {
    id: 'rotate',
    name: 'Rotate PDF',
    description: 'Rotate pages in your PDF document',
    icon: 'rotate',
    path: '/rotate',
    category: 'edit',
    color: 'bg-purple-500'
  },
  {
    id: 'delete',
    name: 'Delete Pages',
    description: 'Remove unwanted pages from your PDF',
    icon: 'delete',
    path: '/delete',
    category: 'edit',
    color: 'bg-red-500'
  },

  // Convert Tools
  {
    id: 'pdf-to-word',
    name: 'PDF to Word',
    description: 'Convert PDF to editable Word document',
    icon: 'word',
    path: '/pdf-to-word',
    category: 'convert',
    color: 'bg-blue-600'
  },
  {
    id: 'word-to-pdf',
    name: 'Word to PDF',
    description: 'Convert Word documents to PDF format',
    icon: 'pdf',
    path: '/word-to-pdf',
    category: 'convert',
    color: 'bg-red-600'
  },
  {
    id: 'pdf-to-excel',
    name: 'PDF to Excel',
    description: 'Convert PDF to Excel spreadsheet',
    icon: 'excel',
    path: '/pdf-to-excel',
    category: 'convert',
    color: 'bg-green-600'
  },
  {
    id: 'excel-to-pdf',
    name: 'Excel to PDF',
    description: 'Convert Excel files to PDF format',
    icon: 'pdf',
    path: '/excel-to-pdf',
    category: 'convert',
    color: 'bg-red-600'
  },
  {
    id: 'pdf-to-ppt',
    name: 'PDF to PowerPoint',
    description: 'Convert PDF to PowerPoint presentation',
    icon: 'powerpoint',
    path: '/pdf-to-ppt',
    category: 'convert',
    color: 'bg-orange-600'
  },
  {
    id: 'ppt-to-pdf',
    name: 'PowerPoint to PDF',
    description: 'Convert PowerPoint to PDF format',
    icon: 'pdf',
    path: '/ppt-to-pdf',
    category: 'convert',
    color: 'bg-red-600'
  },
  {
    id: 'pdf-to-jpg',
    name: 'PDF to JPG',
    description: 'Convert PDF pages to JPG images',
    icon: 'image',
    path: '/pdf-to-jpg',
    category: 'convert',
    color: 'bg-pink-500'
  },
  {
    id: 'jpg-to-pdf',
    name: 'JPG to PDF',
    description: 'Convert JPG images to PDF document',
    icon: 'pdf',
    path: '/jpg-to-pdf',
    category: 'convert',
    color: 'bg-red-600'
  },

  // Optimize Tools
  {
    id: 'compress',
    name: 'Compress PDF',
    description: 'Reduce PDF file size while maintaining quality',
    icon: 'compress',
    path: '/compress',
    category: 'optimize',
    color: 'bg-yellow-500'
  },

  // Edit Tools
  {
    id: 'watermark',
    name: 'Add Watermark',
    description: 'Add text or image watermark to your PDF',
    icon: 'watermark',
    path: '/watermark',
    category: 'edit',
    color: 'bg-indigo-500'
  },
]

export const categories = [
  { id: 'all', name: 'All Tools', count: tools.length },
  { id: 'organize', name: 'Organize', count: tools.filter(t => t.category === 'organize').length },
  { id: 'convert', name: 'Convert', count: tools.filter(t => t.category === 'convert').length },
  { id: 'optimize', name: 'Optimize', count: tools.filter(t => t.category === 'optimize').length },
  { id: 'edit', name: 'Edit', count: tools.filter(t => t.category === 'edit').length },
]
