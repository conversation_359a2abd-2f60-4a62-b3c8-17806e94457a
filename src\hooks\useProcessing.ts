'use client'

import { useState, useCallback } from 'react'
import { ProcessingState, ProcessingStatus } from '@/types'

export function useProcessing() {
  const [state, setState] = useState<ProcessingState>({
    status: 'idle',
    progress: 0,
    message: ''
  })

  const updateStatus = useCallback((
    status: ProcessingStatus,
    progress: number = 0,
    message: string = '',
    result?: Blob,
    error?: string
  ) => {
    setState({
      status,
      progress,
      message,
      result,
      error
    })
  }, [])

  const reset = useCallback(() => {
    setState({
      status: 'idle',
      progress: 0,
      message: ''
    })
  }, [])

  const processWithProgress = useCallback(async <T>(
    operation: () => Promise<T>,
    steps: { message: string; weight: number }[]
  ): Promise<T> => {
    try {
      updateStatus('processing', 0, steps[0]?.message || 'Starting...')
      
      let totalProgress = 0
      const totalWeight = steps.reduce((sum, step) => sum + step.weight, 0)
      
      for (let i = 0; i < steps.length; i++) {
        const step = steps[i]
        updateStatus('processing', totalProgress, step.message)
        
        // Simulate step progress
        const stepProgress = (step.weight / totalWeight) * 100
        const progressIncrement = stepProgress / 10
        
        for (let j = 0; j < 10; j++) {
          await new Promise(resolve => setTimeout(resolve, 50))
          totalProgress += progressIncrement
          updateStatus('processing', Math.min(totalProgress, 100), step.message)
        }
      }
      
      const result = await operation()
      updateStatus('completed', 100, 'Processing completed successfully!', result as any)
      
      return result
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'An unexpected error occurred'
      updateStatus('error', 0, 'Processing failed', undefined, errorMessage)
      throw error
    }
  }, [updateStatus])

  return {
    state,
    updateStatus,
    reset,
    processWithProgress
  }
}
