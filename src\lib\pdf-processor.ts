import { PDFDocument, PDFPage, rgb, StandardFonts } from 'pdf-lib'

export class PDFProcessor {
  /**
   * Merge multiple PDF files into one
   */
  static async mergePDFs(files: File[]): Promise<Blob> {
    const mergedPdf = await PDFDocument.create()
    
    for (const file of files) {
      const arrayBuffer = await file.arrayBuffer()
      const pdf = await PDFDocument.load(arrayBuffer)
      const pages = await mergedPdf.copyPages(pdf, pdf.getPageIndices())
      
      pages.forEach((page) => {
        mergedPdf.addPage(page)
      })
    }
    
    const pdfBytes = await mergedPdf.save()
    return new Blob([pdfBytes], { type: 'application/pdf' })
  }

  /**
   * Split PDF into individual pages
   */
  static async splitPDF(file: File): Promise<Blob[]> {
    const arrayBuffer = await file.arrayBuffer()
    const pdf = await PDFDocument.load(arrayBuffer)
    const pageCount = pdf.getPageCount()
    const splitPdfs: Blob[] = []
    
    for (let i = 0; i < pageCount; i++) {
      const newPdf = await PDFDocument.create()
      const [page] = await newPdf.copyPages(pdf, [i])
      newPdf.addPage(page)
      
      const pdfBytes = await newPdf.save()
      splitPdfs.push(new Blob([pdfBytes], { type: 'application/pdf' }))
    }
    
    return splitPdfs
  }

  /**
   * Extract specific pages from PDF
   */
  static async extractPages(file: File, pageNumbers: number[]): Promise<Blob> {
    const arrayBuffer = await file.arrayBuffer()
    const pdf = await PDFDocument.load(arrayBuffer)
    const newPdf = await PDFDocument.create()
    
    // Convert to 0-based indices and validate
    const validIndices = pageNumbers
      .map(num => num - 1)
      .filter(index => index >= 0 && index < pdf.getPageCount())
    
    if (validIndices.length === 0) {
      throw new Error('No valid page numbers provided')
    }
    
    const pages = await newPdf.copyPages(pdf, validIndices)
    pages.forEach(page => newPdf.addPage(page))
    
    const pdfBytes = await newPdf.save()
    return new Blob([pdfBytes], { type: 'application/pdf' })
  }

  /**
   * Rotate pages in PDF
   */
  static async rotatePDF(file: File, rotation: 90 | 180 | 270, pageNumbers?: number[]): Promise<Blob> {
    const arrayBuffer = await file.arrayBuffer()
    const pdf = await PDFDocument.load(arrayBuffer)
    const pages = pdf.getPages()
    
    const pagesToRotate = pageNumbers 
      ? pageNumbers.map(num => num - 1).filter(index => index >= 0 && index < pages.length)
      : pages.map((_, index) => index)
    
    pagesToRotate.forEach(pageIndex => {
      const page = pages[pageIndex]
      page.setRotation({ angle: rotation, type: 'degrees' })
    })
    
    const pdfBytes = await pdf.save()
    return new Blob([pdfBytes], { type: 'application/pdf' })
  }

  /**
   * Delete pages from PDF
   */
  static async deletePages(file: File, pageNumbers: number[]): Promise<Blob> {
    const arrayBuffer = await file.arrayBuffer()
    const pdf = await PDFDocument.load(arrayBuffer)
    const totalPages = pdf.getPageCount()
    
    // Convert to 0-based indices and sort in descending order
    const indicesToDelete = pageNumbers
      .map(num => num - 1)
      .filter(index => index >= 0 && index < totalPages)
      .sort((a, b) => b - a)
    
    // Remove pages from end to beginning to maintain correct indices
    indicesToDelete.forEach(index => {
      pdf.removePage(index)
    })
    
    const pdfBytes = await pdf.save()
    return new Blob([pdfBytes], { type: 'application/pdf' })
  }

  /**
   * Add text watermark to PDF
   */
  static async addTextWatermark(
    file: File, 
    text: string, 
    options: {
      opacity?: number
      fontSize?: number
      color?: [number, number, number]
      rotation?: number
    } = {}
  ): Promise<Blob> {
    const { opacity = 0.3, fontSize = 50, color = [0.5, 0.5, 0.5], rotation = 45 } = options
    
    const arrayBuffer = await file.arrayBuffer()
    const pdf = await PDFDocument.load(arrayBuffer)
    const font = await pdf.embedFont(StandardFonts.Helvetica)
    const pages = pdf.getPages()
    
    pages.forEach(page => {
      const { width, height } = page.getSize()
      
      page.drawText(text, {
        x: width / 2,
        y: height / 2,
        size: fontSize,
        font,
        color: rgb(color[0], color[1], color[2]),
        opacity,
        rotate: { angle: rotation, type: 'degrees' },
        xSkew: { angle: 0, type: 'degrees' },
        ySkew: { angle: 0, type: 'degrees' },
      })
    })
    
    const pdfBytes = await pdf.save()
    return new Blob([pdfBytes], { type: 'application/pdf' })
  }

  /**
   * Compress PDF (basic compression by reducing quality)
   */
  static async compressPDF(file: File): Promise<Blob> {
    const arrayBuffer = await file.arrayBuffer()
    const pdf = await PDFDocument.load(arrayBuffer)
    
    // Basic compression by re-saving with default compression
    const pdfBytes = await pdf.save({
      useObjectStreams: true,
      addDefaultPage: false,
    })
    
    return new Blob([pdfBytes], { type: 'application/pdf' })
  }

  /**
   * Get PDF metadata
   */
  static async getPDFInfo(file: File): Promise<{
    pageCount: number
    title?: string
    author?: string
    subject?: string
    creator?: string
    producer?: string
    creationDate?: Date
    modificationDate?: Date
  }> {
    const arrayBuffer = await file.arrayBuffer()
    const pdf = await PDFDocument.load(arrayBuffer)
    
    return {
      pageCount: pdf.getPageCount(),
      title: pdf.getTitle(),
      author: pdf.getAuthor(),
      subject: pdf.getSubject(),
      creator: pdf.getCreator(),
      producer: pdf.getProducer(),
      creationDate: pdf.getCreationDate(),
      modificationDate: pdf.getModificationDate(),
    }
  }
}
