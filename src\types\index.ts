export interface PDFFile {
  id: string
  file: File
  name: string
  size: number
  pages?: number
  preview?: string
}

export interface Tool {
  id: string
  name: string
  description: string
  icon: string
  path: string
  category: 'convert' | 'organize' | 'optimize' | 'edit'
  color: string
}

export interface ProcessingStep {
  id: string
  title: string
  description: string
  completed: boolean
  current: boolean
}

export interface ConversionOptions {
  quality?: 'high' | 'medium' | 'low'
  format?: 'jpg' | 'png' | 'docx' | 'xlsx' | 'pptx'
  pageRange?: {
    start: number
    end: number
  }
}

export interface CompressionResult {
  originalSize: number
  compressedSize: number
  compressionRatio: number
}

export type ProcessingStatus = 'idle' | 'uploading' | 'processing' | 'completed' | 'error'

export interface ProcessingState {
  status: ProcessingStatus
  progress: number
  message: string
  result?: Blob
  error?: string
}
